#!/bin/bash

# Array of CAN interfaces to configure
CAN_INTERFACES=("can1" "can2" "can3" "can4")

for interface in "${CAN_INTERFACES[@]}"; do
    echo "Bringing down CAN interface $interface..."
    sudo ip link set $interface down

    echo "Reconfiguring CAN-FD parameters for $interface..."
    # Use parameters from Moteus docs for 80MHz clocks - https://github.com/otaviogood/moteus/blob/main/docs/reference.md
    sudo ip link set $interface type can bitrate 1000000 sjw 10 sample-point 0.666 dbitrate 5000000 dsjw 5 dsample-point 0.666 fd on restart-ms 1000

    echo "Bringing up CAN interface $interface..."
    sudo ip link set $interface up

    echo "Checking CAN interface status for $interface:"
    ip -details link show $interface
    ip -s link show $interface
    echo ""
done

echo "Running temperature monitoring program..."
poetry run python candebug.py

echo "Cleaning up CAN interfaces after program exit..."
for interface in "${CAN_INTERFACES[@]}"; do
    sudo ip link set $interface down
    echo "CAN interface $interface down."
done